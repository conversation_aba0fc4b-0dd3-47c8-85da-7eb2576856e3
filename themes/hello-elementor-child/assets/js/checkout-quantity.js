jQuery(document).ready(function($) {
    'use strict';

    console.log('Checkout quantity script loaded');
    
    // Listen for checkout errors
    $(document.body).on('checkout_error', function(event, error_message) {
        console.log('Checkout error:', error_message);
        
        // Display error in notices wrapper
        if (error_message) {
            $('.woocommerce-notices-wrapper').html('<ul class="woocommerce-error" role="alert"><li>' + error_message + '</li></ul>');
        }
    });
    
    // Listen for applied coupon messages
    $(document.body).on('applied_coupon_in_checkout', function(event, coupon_code) {
        console.log('Coupon applied in checkout:', coupon_code);
    });
    
    // Listen for removed coupon messages  
    $(document.body).on('removed_coupon_in_checkout', function(event, coupon_code) {
        console.log('Coupon removed in checkout:', coupon_code);
    });

    // Handle coupon form submission
    $(document).on('submit', 'form.checkout_coupon, form.coupon', function(e) {
        e.preventDefault();
        return false;
    });

    // Handle coupon application
    $(document).on('click', '#apply-coupon-btn', function(e) {
        e.preventDefault();
        
        console.log('Apply coupon clicked');
        
        var $button = $(this);
        var $input = $('#coupon_code');
        var couponCode = $input.val().trim();
        
        if (!couponCode) {
            console.log('No coupon code entered');
            return;
        }
        
        console.log('Applying coupon:', couponCode);
        
        // Disable button and show loading state
        $button.prop('disabled', true);
        var originalHtml = $button.html();
        $button.text('Applying...');
        
        // Block UI
        $('.checkout-order-review').block({
            message: null,
            overlayCSS: {
                background: '#fff',
                opacity: 0.6
            }
        });
        
        // Method 1: Try using WooCommerce's built-in checkout update with coupon
        var checkout_form = $('form.checkout');
        
        // Add a hidden input for the coupon
        if (!checkout_form.find('input[name="coupon_code"]').length) {
            checkout_form.append('<input type="hidden" name="coupon_code" value="' + couponCode + '" />');
        } else {
            checkout_form.find('input[name="coupon_code"]').val(couponCode);
        }
        
        // Trigger checkout update which will process the coupon
        console.log('Triggering update_checkout with coupon');
        $(document.body).trigger('update_checkout');
        
        // Wait for the update to complete
        $(document.body).one('updated_checkout', function() {
            console.log('Checkout updated after coupon');
            
            // Clear the coupon input
            $('#coupon_code').val('');
            
            // Remove the hidden input
            checkout_form.find('input[name="coupon_code"]').remove();
            
            // Unblock and reset button
            $('.checkout-order-review').unblock();
            $button.prop('disabled', false).html(originalHtml);
        });
        
        // Fallback timeout in case updated_checkout doesn't fire
        setTimeout(function() {
            if ($button.prop('disabled')) {
                console.log('Fallback: resetting button after timeout');
                $('.checkout-order-review').unblock();
                $button.prop('disabled', false).html(originalHtml);
                checkout_form.find('input[name="coupon_code"]').remove();
            }
        }, 5000);
    });

    // Allow Enter key to apply coupon
    $('#coupon_code').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $('#apply-coupon-btn').click();
        }
    });
    
    // Handle coupon removal
    $(document).on('click', '.woocommerce-remove-coupon', function(e) {
        e.preventDefault();
        
        console.log('Remove coupon clicked');
        
        var $link = $(this);
        var coupon = $link.data('coupon');
        
        if (!coupon) {
            // Try to get coupon from href
            var href = $link.attr('href');
            console.log('Coupon link href:', href);
            var match = href.match(/remove_coupon=([^&]+)/);
            if (match) {
                coupon = decodeURIComponent(match[1]);
            }
        }
        
        if (!coupon) {
            console.log('No coupon code found');
            return false;
        }
        
        console.log('Removing coupon:', coupon);
        
        // Block UI
        $('.checkout-order-review').block({
            message: null,
            overlayCSS: {
                background: '#fff',
                opacity: 0.6
            }
        });
        
        // Method: Add remove_coupon parameter and trigger checkout update
        var checkout_form = $('form.checkout');
        
        // Add a hidden input for remove_coupon
        if (!checkout_form.find('input[name="remove_coupon"]').length) {
            checkout_form.append('<input type="hidden" name="remove_coupon" value="' + coupon + '" />');
        } else {
            checkout_form.find('input[name="remove_coupon"]').val(coupon);
        }
        
        // Trigger checkout update which will process the removal
        console.log('Triggering update_checkout to remove coupon');
        $(document.body).trigger('update_checkout');
        
        // Wait for the update to complete
        $(document.body).one('updated_checkout', function() {
            console.log('Checkout updated after coupon removal');
            
            // Remove the hidden input
            checkout_form.find('input[name="remove_coupon"]').remove();
            
            // Unblock
            $('.checkout-order-review').unblock();
        });
        
        // Fallback timeout
        setTimeout(function() {
            $('.checkout-order-review').unblock();
            checkout_form.find('input[name="remove_coupon"]').remove();
        }, 5000);
        
        return false;
    });
});
